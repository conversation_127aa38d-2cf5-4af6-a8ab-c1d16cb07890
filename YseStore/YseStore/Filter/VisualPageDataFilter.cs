using Microsoft.AspNetCore.Mvc.Filters;
using YseStore.Common;
using YseStore.IService.Visual;

namespace YseStore.Filter
{
    /// <summary>
    /// 全局可视化页面数据过滤器
    /// 用于在所有控制器动作中自动获取可视化页面数据并共享给前端页面
    /// </summary>
    public class VisualPageDataFilter : IAsyncActionFilter
    {
        private readonly IVisualPageBuilderService _visualPageBuilderService;
        private readonly ILogger<VisualPageDataFilter> _logger;

        public VisualPageDataFilter(
            IVisualPageBuilderService visualPageBuilderService,
            ILogger<VisualPageDataFilter> logger)
        {
            _visualPageBuilderService = visualPageBuilderService;
            _logger = logger;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            try
            {
                // 获取当前页面类型
                string pageType = GetPageTypeFromContext(context);
                
                if (!string.IsNullOrEmpty(pageType))
                {
                    // 获取可视化页面数据
                    var visualPageData = await GetVisualPageDataAsync(pageType);
                    
                    if (visualPageData != null)
                    {
                        // 使用专门处理JSON字符串字段的序列化方法
                        string json = visualPageData.ToJsonWithParsedFields();
                        _logger.LogInformation($"可视化页面数据: {json}");
                        // 将数据添加到ViewData中供前端使用
                        if (context.Controller is Controller controller)
                        {
                            controller.ViewData["VisualPageData"] = json;
                            controller.ViewData["VisualPageDataObject"] = visualPageData;
                        }
                        
                        _logger.LogDebug("成功获取页面 {PageType} 的可视化数据", pageType);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可视化页面数据时发生异常");
                // 不阻止请求继续执行，只是记录错误
            }

            // 继续执行动作
            await next();
        }

        /// <summary>
        /// 获取可视化页面数据
        /// </summary>
        /// <param name="pages">页面类型</param>
        /// <returns></returns>
        private async Task<object> GetVisualPageDataAsync(string pages)
        {
            try
            {
                var visualPageData = await _visualPageBuilderService.BuildVisualPageAsync(pages);
                return visualPageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "构建可视化页面数据失败，页面类型: {Pages}", pages);
                // 返回空对象
                return new { };
            }
        }

        /// <summary>
        /// 根据上下文获取页面类型
        /// </summary>
        /// <param name="context">动作执行上下文</param>
        /// <returns>页面类型</returns>
        private string GetPageTypeFromContext(ActionExecutingContext context)
        {
            try
            {
                var controllerName = context.RouteData.Values["controller"]?.ToString()?.ToLower();
                var actionName = context.RouteData.Values["action"]?.ToString()?.ToLower();

                // 根据控制器和动作名称映射到页面类型
                return (controllerName, actionName) switch
                {
                    ("home", "index") => "index",
                    ("product", _) => "products",
                    ("products", _) => "products", 
                    ("blog", _) => "blog",
                    ("page", _) => "page",
                    ("cart", _) => "cart",
                    ("checkout", _) => "checkout",
                    ("account", _) => "account",
                    ("search", _) => "search",
                    ("collections", _) => "collections",
                    _ => "index" // 默认使用首页数据
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取页面类型时发生异常，使用默认值 'index'");
                return "index";
            }
        }
    }
}
